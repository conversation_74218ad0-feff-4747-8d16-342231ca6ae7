/* Careers Page Specific Styles */

/* Job Listing Cards */
.job-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #E5E7EB;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.job-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border-color: #6366F1;
}

.job-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.job-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.5rem;
}

.job-department {
    color: #6366F1;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.job-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #6B7280;
}

.job-meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.job-description {
    color: #4B5563;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.job-requirements {
    margin-bottom: 1.5rem;
}

.job-requirements h4 {
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.75rem;
}

.job-requirements ul {
    list-style: none;
    padding: 0;
}

.job-requirements li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.5rem;
    color: #6B7280;
}

.job-requirements li::before {
    content: '•';
    color: #6366F1;
    font-weight: bold;
    margin-right: 0.75rem;
    margin-top: 0.125rem;
}

.job-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.apply-btn {
    background: linear-gradient(135deg, #6366F1, #8B5CF6);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.apply-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.save-btn {
    background: transparent;
    color: #6B7280;
    border: 2px solid #E5E7EB;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-btn:hover {
    border-color: #6366F1;
    color: #6366F1;
}

/* Benefits Section */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.benefit-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.benefit-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
}

.benefit-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.75rem;
}

.benefit-description {
    color: #6B7280;
    line-height: 1.6;
}

/* Application Form */
.application-form {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    margin: 2rem 0;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.5rem;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #E5E7EB;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: #6366F1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.file-upload {
    border: 2px dashed #D1D5DB;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-upload:hover {
    border-color: #6366F1;
    background: #F8FAFC;
}

.file-upload.dragover {
    border-color: #6366F1;
    background: #EEF2FF;
}

/* Responsive Design */
@media (max-width: 768px) {
    .job-header {
        flex-direction: column;
    }
    
    .job-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .job-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .apply-btn,
    .save-btn {
        text-align: center;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .application-form {
        padding: 1.5rem;
    }
}

@media (max-width: 640px) {
    .job-card {
        padding: 1.5rem;
    }
    
    .job-title {
        font-size: 1.25rem;
    }
    
    .benefit-item {
        padding: 1.5rem;
    }
    
    .file-upload {
        padding: 1.5rem;
    }
}
