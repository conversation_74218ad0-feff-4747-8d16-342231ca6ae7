/* Life@ Pracinov Page Specific Styles */

/* Culture Section */
.culture-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.culture-item {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: transform 0.3s ease;
}

.culture-item:hover {
    transform: translateY(-4px);
}

.culture-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
}

.culture-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 1rem;
}

.culture-description {
    color: #6B7280;
    line-height: 1.6;
}

/* Photo Gallery */
.photo-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.gallery-item {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

/* Team Events */
.event-timeline {
    position: relative;
    padding: 2rem 0;
}

.event-item {
    display: flex;
    margin-bottom: 2rem;
    align-items: flex-start;
}

.event-date {
    background: linear-gradient(135deg, #6366F1, #8B5CF6);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    min-width: 120px;
    text-align: center;
    margin-right: 2rem;
}

.event-content {
    flex: 1;
    background: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.event-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.5rem;
}

.event-description {
    color: #6B7280;
    line-height: 1.6;
}

/* Perks & Benefits */
.perks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.perk-item {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.perk-item:hover {
    border-color: #6366F1;
    transform: translateY(-2px);
}

.perk-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #6366F1;
}

.perk-title {
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.5rem;
}

.perk-description {
    color: #6B7280;
    font-size: 0.875rem;
}

/* Testimonials from Employees */
.employee-testimonial {
    background: linear-gradient(135deg, #F8FAFC, #E2E8F0);
    border-radius: 1rem;
    padding: 2rem;
    margin: 2rem 0;
    position: relative;
}

.employee-testimonial::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: #6366F1;
    opacity: 0.3;
}

.testimonial-content {
    font-size: 1.125rem;
    color: #4B5563;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #6366F1, #8B5CF6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.25rem;
}

.author-info h4 {
    font-weight: 600;
    color: #1F2937;
    margin: 0;
}

.author-info p {
    color: #6B7280;
    margin: 0;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .culture-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .photo-gallery {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .event-item {
        flex-direction: column;
        align-items: stretch;
    }
    
    .event-date {
        margin-right: 0;
        margin-bottom: 1rem;
        align-self: flex-start;
    }
    
    .perks-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .employee-testimonial {
        padding: 1.5rem;
    }
}

@media (max-width: 640px) {
    .photo-gallery {
        grid-template-columns: 1fr;
    }
    
    .perks-grid {
        grid-template-columns: 1fr;
    }
    
    .culture-item {
        padding: 1.5rem;
    }
    
    .event-content {
        padding: 1rem;
    }
}
