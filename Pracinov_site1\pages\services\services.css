/* Services Page Specific Styles */

/* Service Cards */
.service-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid #E5E7EB;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #6366F1;
}

.service-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.service-features li {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: #6B7280;
}

.service-features li::before {
    content: '✓';
    color: #10B981;
    font-weight: bold;
    margin-right: 0.75rem;
    font-size: 1.125rem;
}

/* Process Section */
.process-step {
    text-align: center;
    position: relative;
}

.process-number {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #6366F1, #8B5CF6);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.25rem;
    margin: 0 auto 1rem;
}

.process-connector {
    position: absolute;
    top: 1.5rem;
    left: 50%;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, #6366F1, #8B5CF6);
    z-index: -1;
}

.process-step:last-child .process-connector {
    display: none;
}

/* Technology Stack */
.tech-stack {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.tech-item {
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.tech-item:hover {
    transform: translateY(-4px);
}

.tech-logo {
    width: 3rem;
    height: 3rem;
    margin: 0 auto 0.5rem;
    background: #F3F4F6;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .process-connector {
        display: none;
    }
    
    .tech-stack {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 1rem;
    }
    
    .service-card {
        padding: 1.5rem;
    }
}

@media (max-width: 640px) {
    .process-number {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
    
    .tech-stack {
        grid-template-columns: repeat(3, 1fr);
    }
}
