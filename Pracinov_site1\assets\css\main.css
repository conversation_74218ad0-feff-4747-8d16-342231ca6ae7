/* Pracinov Website - Main Styles */

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #F3F4F6;
    line-height: 1.6;
    color: #374151;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

p {
    margin-bottom: 1rem;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(to right, #6366F1, #8B5CF6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Header Styles */
.header-gradient {
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
}

/* Navigation */
.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nav-link:hover {
    color: white;
    text-decoration: none;
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
}

.nav-link.active {
    color: white;
    font-weight: 700;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -4px;
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.7);
}

/* Feature Cards */
.feature-card {
    background-color: #FFFFFF;
    border-radius: 1rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 3px 6px rgba(0, 0, 0, 0.03);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    position: relative;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 4px;
    width: 0;
    background: linear-gradient(to right, #6366F1, #8B5CF6);
    transition: width 0.4s ease;
}

.feature-card:hover::before {
    width: 100%;
}

.feature-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08), 0 5px 10px rgba(0, 0, 0, 0.05);
}

/* Footer */
.footer-bg {
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn:hover {
    text-decoration: none;
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #4F46E5;
    border: 2px solid transparent;
}

.btn-secondary:hover {
    background: white;
    color: #4F46E5;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.3s ease;
}

.notification-success {
    background: #10B981;
    color: white;
}

.notification-error {
    background: #EF4444;
    color: white;
}

.notification-content {
    display: flex;
    align-items: center;
    padding: 1rem;
    gap: 0.75rem;
}

.notification-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    margin-left: auto;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .nav-link {
        padding: 0.75rem 1rem;
        font-size: 1.125rem;
    }
    
    .btn {
        padding: 0.875rem 1.75rem;
        font-size: 1.125rem;
    }
}

@media (max-width: 640px) {
    .feature-card {
        margin-bottom: 1.5rem;
    }
    
    .notification {
        left: 20px;
        right: 20px;
        max-width: none;
    }
}

/* Scroll to Top Button */
#scroll-to-top {
    transition: all 0.3s ease;
}

#scroll-to-top:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 25px -5px rgba(0, 0, 0, 0.2);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4F46E5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
