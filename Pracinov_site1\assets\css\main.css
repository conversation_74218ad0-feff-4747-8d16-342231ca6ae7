/* Pracinov Website - Modern Design System */

/* ===== STEP 1: CSS RESET & FOUNDATION ===== */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #FAFBFC;
    line-height: 1.6;
    color: #1F2937;
    overflow-x: hidden;
}

/* ===== STEP 2: SPACING SYSTEM ===== */
:root {
    /* Base spacing units */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */
    --space-4xl: 6rem;      /* 96px */
    --space-5xl: 8rem;      /* 128px */

    /* Container boundaries */
    --container-xs: 480px;
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1400px;
    --container-max: 1600px;

    /* Content width limits */
    --content-narrow: 65ch;
    --content-wide: 85ch;

    /* Border radius system */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Shadow system */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Color system */
    --color-primary: #4F46E5;
    --color-primary-dark: #3730A3;
    --color-secondary: #7C3AED;
    --color-accent: #06B6D4;
    --color-success: #10B981;
    --color-warning: #F59E0B;
    --color-error: #EF4444;

    /* Neutral colors */
    --color-white: #FFFFFF;
    --color-gray-50: #F9FAFB;
    --color-gray-100: #F3F4F6;
    --color-gray-200: #E5E7EB;
    --color-gray-300: #D1D5DB;
    --color-gray-400: #9CA3AF;
    --color-gray-500: #6B7280;
    --color-gray-600: #4B5563;
    --color-gray-700: #374151;
    --color-gray-800: #1F2937;
    --color-gray-900: #111827;
}

/* ===== STEP 1 & 4: CONTAINER BOUNDARIES ===== */
.container {
    width: 100%;
    max-width: var(--container-2xl);
    margin: 0 auto;
    padding-left: var(--space-md);
    padding-right: var(--space-md);
}

@media (min-width: 640px) {
    .container {
        padding-left: var(--space-lg);
        padding-right: var(--space-lg);
    }
}

@media (min-width: 1024px) {
    .container {
        padding-left: var(--space-xl);
        padding-right: var(--space-xl);
    }
}

@media (min-width: 1400px) {
    .container {
        padding-left: var(--space-3xl);
        padding-right: var(--space-3xl);
    }
}

/* ===== STEP 7: TYPOGRAPHY SYSTEM ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--space-lg);
    color: var(--color-gray-900);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    letter-spacing: -0.025em;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    letter-spacing: -0.025em;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
}

p {
    margin-bottom: var(--space-lg);
    max-width: var(--content-wide);
    color: var(--color-gray-600);
    font-size: 1.125rem;
    line-height: 1.7;
}

.text-center p {
    margin-left: auto;
    margin-right: auto;
}

/* ===== STEP 3 & 5: SECTIONAL CONTAINMENT ===== */
.section {
    padding: var(--space-4xl) 0;
    position: relative;
}

.section-contained {
    background: var(--color-white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    margin: var(--space-2xl) 0;
    padding: var(--space-3xl);
    border: 1px solid var(--color-gray-100);
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-4xl);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-header h2 {
    margin-bottom: var(--space-lg);
}

.section-header p {
    font-size: 1.25rem;
    color: var(--color-gray-600);
}

/* ===== STEP 8: GRID SYSTEM ===== */
.grid {
    display: grid;
    gap: var(--space-xl);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* ===== STEP 3 & 7: MODERN CARDS ===== */
.card {
    background: var(--color-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-gray-100);
    padding: var(--space-2xl);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--color-gray-200);
}

.card:hover::before {
    transform: scaleX(1);
}

/* ===== STEP 11: INTERACTIVE ELEMENTS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-lg) var(--space-2xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    cursor: pointer;
    font-size: 1rem;
    min-height: 48px; /* Accessibility: minimum touch target */
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    text-decoration: none;
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    color: var(--color-white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
}

.btn-secondary {
    background: var(--color-white);
    color: var(--color-primary);
    border-color: var(--color-gray-200);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--color-gray-50);
    border-color: var(--color-primary);
    box-shadow: var(--shadow-md);
}

/* ===== NAVIGATION SYSTEM ===== */
.header-gradient {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-link {
    color: var(--color-white);
    text-decoration: none;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-height: 44px; /* Touch target */
    display: flex;
    align-items: center;
}

.nav-link:hover {
    color: var(--color-white);
    text-decoration: none;
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.nav-link.active {
    color: var(--color-white);
    font-weight: 700;
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -2px;
    width: 6px;
    height: 6px;
    background: var(--color-white);
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }

.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.z-10 { z-index: 10; }
.z-50 { z-index: 50; }

/* ===== STEP 9: BREATHING ROOM ===== */
.space-y-sm > * + * { margin-top: var(--space-sm); }
.space-y-md > * + * { margin-top: var(--space-md); }
.space-y-lg > * + * { margin-top: var(--space-lg); }
.space-y-xl > * + * { margin-top: var(--space-xl); }

.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }
.mb-2xl { margin-bottom: var(--space-2xl); }

.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }
.mt-2xl { margin-top: var(--space-2xl); }

/* ===== STEP 10: ANIMATIONS & VISUAL RHYTHM ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* ===== FOOTER SYSTEM ===== */
.footer-bg {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    position: relative;
}

.footer-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* ===== SCROLL TO TOP ===== */
#scroll-to-top {
    position: fixed;
    bottom: var(--space-xl);
    right: var(--space-xl);
    background: var(--color-primary);
    color: var(--color-white);
    border: none;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    display: none;
}

#scroll-to-top:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    background: var(--color-primary-dark);
}

/* ===== STEP 6 & 12: RESPONSIVE GUTTER STRATEGY ===== */
@media (max-width: 480px) {
    .container {
        padding-left: var(--space-sm);
        padding-right: var(--space-sm);
    }

    .section {
        padding: var(--space-2xl) 0;
    }

    .section-contained {
        padding: var(--space-xl);
        margin: var(--space-md) 0;
        border-radius: var(--radius-lg);
    }

    .card {
        padding: var(--space-lg);
    }

    .btn {
        padding: var(--space-md) var(--space-lg);
        font-size: 0.875rem;
    }

    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }

    p {
        font-size: 1rem;
        line-height: 1.6;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    .section {
        padding: var(--space-3xl) 0;
    }

    .grid {
        gap: var(--space-lg);
    }
}

@media (min-width: 1600px) {
    .container {
        max-width: var(--container-max);
    }

    .section {
        padding: var(--space-5xl) 0;
    }

    .section-contained {
        padding: var(--space-4xl);
    }
}

/* ===== SPECIAL COMPONENTS ===== */
.hero-section {
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.4;
}

.feature-card {
    /* Alias for .card with specific styling */
    background: var(--color-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-gray-100);
    padding: var(--space-2xl);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--color-gray-200);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

/* ===== ACCESSIBILITY & FOCUS STATES ===== */
*:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

.btn:focus,
.nav-link:focus {
    outline: 2px solid var(--color-white);
    outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
    .header-gradient,
    .footer-bg,
    #scroll-to-top,
    .btn {
        display: none !important;
    }

    .section {
        padding: var(--space-md) 0;
        break-inside: avoid;
    }

    .card {
        border: 1px solid var(--color-gray-300);
        box-shadow: none;
        break-inside: avoid;
    }
}

/* ===== IMPROVED GUTTER SYSTEM & REDUCED WHITE SPACE ===== */
/* Ensure content never touches browser edges while reducing excessive spacing */

/* Override hero section to reduce height */
.hero-section {
    min-height: 85vh !important;
    padding-top: 6rem !important;
    padding-bottom: 3rem !important;
}

/* Reduce section padding for tighter layout */
.section {
    padding: var(--space-3xl) 0 !important;
}

/* Compact grid spacing */
.grid {
    gap: var(--space-lg) !important;
}

@media (min-width: 768px) {
    .grid {
        gap: var(--space-xl) !important;
    }
}

/* Ensure proper horizontal gutters */
body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Prevent content from touching edges */
section {
    padding-left: max(var(--space-md), env(safe-area-inset-left)) !important;
    padding-right: max(var(--space-md), env(safe-area-inset-right)) !important;
}

@media (min-width: 640px) {
    section {
        padding-left: max(var(--space-lg), env(safe-area-inset-left)) !important;
        padding-right: max(var(--space-lg), env(safe-area-inset-right)) !important;
    }
}

/* Compact mobile spacing */
@media (max-width: 640px) {
    .hero-section {
        min-height: 70vh !important;
        padding-top: 5rem !important;
        padding-bottom: 2rem !important;
    }

    .section {
        padding: var(--space-2xl) 0 !important;
    }

    .grid {
        gap: var(--space-md) !important;
    }
}

/* Reduce footer spacing */
.footer-bg {
    padding: var(--space-2xl) 0 !important;
}

/* Tighter section headers */
.section-header {
    margin-bottom: var(--space-2xl) !important;
}

@media (min-width: 768px) {
    .section-header {
        margin-bottom: var(--space-3xl) !important;
    }
}
