<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Careers - Pracinov</title>
    <meta name="description" content="Join <PERSON>'s team of innovative professionals. Explore exciting career opportunities in software development, cloud solutions, and IT consulting.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="/assets/css/styles.css">
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    <span class="material-icons">integration_instructions</span>
                    <span>Pracinov</span>
                </a>
                
                <nav class="nav hidden md:flex">
                    <a href="index.html" class="nav-link">Home</a>
                    <a href="about.html" class="nav-link">About Us</a>
                    <a href="portfolio.html" class="nav-link">Portfolio</a>
                    <a href="services.html" class="nav-link">Services</a>
                    <a href="life.html" class="nav-link">Life@ Pracinov</a>
                    <a href="careers.html" class="nav-link active">Careers</a>
                </nav>
                
                <button class="btn btn-secondary hidden md:block">
                    Book Consultation
                </button>
                
                <button class="mobile-menu-btn md:hidden" id="mobile-menu-button">
                    <span class="material-icons">menu</span>
                </button>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div class="mobile-menu md:hidden" id="mobile-menu">
            <div class="container">
                <nav class="flex flex-col space-y-2">
                    <a href="index.html" class="nav-link">Home</a>
                    <a href="about.html" class="nav-link">About Us</a>
                    <a href="portfolio.html" class="nav-link">Portfolio</a>
                    <a href="services.html" class="nav-link">Services</a>
                    <a href="life.html" class="nav-link">Life@ Pracinov</a>
                    <a href="careers.html" class="nav-link active">Careers</a>
                    <button class="btn btn-secondary mt-4">Book Consultation</button>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="hero">
            <div class="container">
                <div class="text-center">
                    <h1 class="text-5xl font-bold mb-6">Join Our Team</h1>
                    <p class="text-xl opacity-90 max-w-3xl mx-auto">Build your career with a company that values innovation, growth, and making a real impact in the world of technology.</p>
                </div>
            </div>
        </section>

        <!-- Why Join Us Section -->
        <section class="section">
            <div class="container">
                <div class="section-header">
                    <h2>Why Choose Pracinov?</h2>
                    <p>More than just a job - it's a career-defining opportunity</p>
                </div>
                
                <div class="grid grid-3 gap-8">
                    <div class="card p-6 text-center">
                        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 text-white mb-4 mx-auto">
                            <span class="material-icons">trending_up</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Career Growth</h3>
                        <p class="text-gray-600 text-sm">Clear career progression paths with mentorship, training, and leadership opportunities to help you reach your full potential.</p>
                    </div>
                    
                    <div class="card p-6 text-center">
                        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-green-500 to-emerald-600 text-white mb-4 mx-auto">
                            <span class="material-icons">lightbulb</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Innovation Focus</h3>
                        <p class="text-gray-600 text-sm">Work on cutting-edge projects using the latest technologies and methodologies in a forward-thinking environment.</p>
                    </div>
                    
                    <div class="card p-6 text-center">
                        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 text-white mb-4 mx-auto">
                            <span class="material-icons">balance</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Work-Life Balance</h3>
                        <p class="text-gray-600 text-sm">Flexible schedules, remote work options, and unlimited PTO to help you maintain a healthy work-life balance.</p>
                    </div>
                    
                    <div class="card p-6 text-center">
                        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-orange-500 to-red-600 text-white mb-4 mx-auto">
                            <span class="material-icons">groups</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Amazing Team</h3>
                        <p class="text-gray-600 text-sm">Join a diverse, talented team of professionals who support each other and celebrate collective success.</p>
                    </div>
                    
                    <div class="card p-6 text-center">
                        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-teal-500 to-blue-600 text-white mb-4 mx-auto">
                            <span class="material-icons">school</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Learning Budget</h3>
                        <p class="text-gray-600 text-sm">$3,000 annual learning budget for courses, conferences, certifications, and professional development.</p>
                    </div>
                    
                    <div class="card p-6 text-center">
                        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 text-white mb-4 mx-auto">
                            <span class="material-icons">public</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Global Impact</h3>
                        <p class="text-gray-600 text-sm">Work on projects that make a difference for clients around the world and contribute to meaningful technological advancement.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Open Positions -->
        <section class="section bg-gray-50">
            <div class="container">
                <div class="section-header">
                    <h2>Open Positions</h2>
                    <p>Find your next career opportunity with us</p>
                </div>
                
                <div class="space-y-6">
                    <!-- Job 1 -->
                    <div class="card p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="text-xl font-semibold mb-2">Senior Full-Stack Developer</h3>
                                <div class="flex items-center gap-4 text-sm text-gray-600">
                                    <span class="flex items-center"><span class="material-icons text-sm mr-1">location_on</span>Remote / San Francisco</span>
                                    <span class="flex items-center"><span class="material-icons text-sm mr-1">schedule</span>Full-time</span>
                                    <span class="flex items-center"><span class="material-icons text-sm mr-1">work</span>5+ years experience</span>
                                </div>
                            </div>
                            <button class="btn btn-primary">Apply Now</button>
                        </div>
                        <p class="text-gray-600 mb-4">We're looking for an experienced full-stack developer to join our team and work on exciting client projects using React, Node.js, and cloud technologies.</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">React</span>
                            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Node.js</span>
                            <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">TypeScript</span>
                            <span class="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">AWS</span>
                        </div>
                    </div>
                    
                    <!-- Job 2 -->
                    <div class="card p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="text-xl font-semibold mb-2">DevOps Engineer</h3>
                                <div class="flex items-center gap-4 text-sm text-gray-600">
                                    <span class="flex items-center"><span class="material-icons text-sm mr-1">location_on</span>Remote / New York</span>
                                    <span class="flex items-center"><span class="material-icons text-sm mr-1">schedule</span>Full-time</span>
                                    <span class="flex items-center"><span class="material-icons text-sm mr-1">work</span>3+ years experience</span>
                                </div>
                            </div>
                            <button class="btn btn-primary">Apply Now</button>
                        </div>
                        <p class="text-gray-600 mb-4">Join our infrastructure team to build and maintain scalable cloud solutions, implement CI/CD pipelines, and ensure high availability for our applications.</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Docker</span>
                            <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">Kubernetes</span>
                            <span class="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">AWS</span>
                            <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">Jenkins</span>
                        </div>
                    </div>
                    
                    <!-- Job 3 -->
                    <div class="card p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="text-xl font-semibold mb-2">UX/UI Designer</h3>
                                <div class="flex items-center gap-4 text-sm text-gray-600">
                                    <span class="flex items-center"><span class="material-icons text-sm mr-1">location_on</span>Remote / Austin</span>
                                    <span class="flex items-center"><span class="material-icons text-sm mr-1">schedule</span>Full-time</span>
                                    <span class="flex items-center"><span class="material-icons text-sm mr-1">work</span>3+ years experience</span>
                                </div>
                            </div>
                            <button class="btn btn-primary">Apply Now</button>
                        </div>
                        <p class="text-gray-600 mb-4">Create beautiful, intuitive user experiences for web and mobile applications. Work closely with development teams to bring designs to life.</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-pink-100 text-pink-800 rounded-full text-sm">Figma</span>
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Adobe Creative Suite</span>
                            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Prototyping</span>
                            <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">User Research</span>
                        </div>
                    </div>
                    
                    <!-- Job 4 -->
                    <div class="card p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="text-xl font-semibold mb-2">Product Manager</h3>
                                <div class="flex items-center gap-4 text-sm text-gray-600">
                                    <span class="flex items-center"><span class="material-icons text-sm mr-1">location_on</span>Remote / Boston</span>
                                    <span class="flex items-center"><span class="material-icons text-sm mr-1">schedule</span>Full-time</span>
                                    <span class="flex items-center"><span class="material-icons text-sm mr-1">work</span>4+ years experience</span>
                                </div>
                            </div>
                            <button class="btn btn-primary">Apply Now</button>
                        </div>
                        <p class="text-gray-600 mb-4">Lead product strategy and roadmap development for our client solutions. Work with cross-functional teams to deliver exceptional products.</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Product Strategy</span>
                            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Agile</span>
                            <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">Analytics</span>
                            <span class="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">Roadmapping</span>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-12">
                    <p class="text-gray-600 mb-4">Don't see a position that fits? We're always looking for talented individuals.</p>
                    <button class="btn btn-secondary">Send Us Your Resume</button>
                </div>
            </div>
        </section>

        <!-- Application Process -->
        <section class="section">
            <div class="container">
                <div class="section-header">
                    <h2>Our Hiring Process</h2>
                    <p>A transparent, fair process designed to find the best fit for both you and our team</p>
                </div>
                
                <div class="grid grid-4 gap-6">
                    <div class="text-center">
                        <div class="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 text-white mb-4 mx-auto">
                            <span class="text-2xl font-bold">1</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Application</h3>
                        <p class="text-gray-600 text-sm">Submit your application with resume and cover letter. We review all applications within 48 hours.</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 text-white mb-4 mx-auto">
                            <span class="text-2xl font-bold">2</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Phone Screen</h3>
                        <p class="text-gray-600 text-sm">Initial conversation with our HR team to discuss your background, interests, and the role.</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 text-white mb-4 mx-auto">
                            <span class="text-2xl font-bold">3</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Technical Interview</h3>
                        <p class="text-gray-600 text-sm">Technical discussion and/or coding challenge with team members to assess your skills and approach.</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 text-white mb-4 mx-auto">
                            <span class="text-2xl font-bold">4</span>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">Final Interview</h3>
                        <p class="text-gray-600 text-sm">Meet with team leads and discuss culture fit, career goals, and answer any questions you have.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Form -->
        <section class="section bg-gray-50">
            <div class="container">
                <div class="max-w-2xl mx-auto">
                    <div class="section-header">
                        <h2>Interested in Joining Us?</h2>
                        <p>Get in touch and let's start a conversation about your career</p>
                    </div>
                    
                    <form class="contact-form card p-8">
                        <div class="grid grid-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                                <input type="text" id="name" name="name" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                                <input type="email" id="email" name="email" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <input type="tel" id="phone" name="phone" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        </div>
                        
                        <div class="mt-6">
                            <label for="position" class="block text-sm font-medium text-gray-700 mb-2">Position of Interest</label>
                            <select id="position" name="position" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                                <option value="">Select a position</option>
                                <option value="senior-fullstack">Senior Full-Stack Developer</option>
                                <option value="devops">DevOps Engineer</option>
                                <option value="ux-designer">UX/UI Designer</option>
                                <option value="product-manager">Product Manager</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div class="mt-6">
                            <label for="resume" class="block text-sm font-medium text-gray-700 mb-2">Resume/CV</label>
                            <input type="file" id="resume" name="resume" accept=".pdf,.doc,.docx" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        </div>
                        
                        <div class="mt-6">
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Cover Letter / Message *</label>
                            <textarea id="message" name="message" rows="5" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="Tell us about yourself and why you're interested in joining Pracinov..."></textarea>
                        </div>
                        
                        <div class="mt-8">
                            <button type="submit" class="btn btn-primary w-full py-4 text-lg">Submit Application</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Pracinov</h3>
                    <p style="color: rgba(255, 255, 255, 0.8);">Premium IT solutions for forward-thinking businesses. Your partner in digital transformation and growth.</p>
                </div>
                <div class="footer-section">
                    <h3>Services</h3>
                    <ul>
                        <li><a href="services.html">Software Development</a></li>
                        <li><a href="services.html">Cloud Solutions</a></li>
                        <li><a href="services.html">IT Consulting</a></li>
                        <li><a href="services.html">Digital Transformation</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="careers.html">Careers</a></li>
                        <li><a href="life.html">Life@ Pracinov</a></li>
                        <li><a href="portfolio.html">Portfolio</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact</h3>
                    <ul>
                        <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li><a href="tel:+1234567890">+1 (234) 567-890</a></li>
                        <li>123 Business Ave, Tech City, TC 12345</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Pracinov. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button id="scroll-to-top" onclick="scrollToTop()" style="display: none; position: fixed; bottom: 20px; right: 20px; background: var(--primary-color); color: white; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; box-shadow: var(--shadow-lg); z-index: 1000;">
        <span class="material-icons">keyboard_arrow_up</span>
    </button>

    <!-- Scripts -->
    <script src="/assets/js/main.js"></script>
</body>
</html>
