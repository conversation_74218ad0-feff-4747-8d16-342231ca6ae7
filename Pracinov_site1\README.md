# Pracinov Website

A modern, responsive website for Pracinov - Premium IT Solutions company. Built with clean HTML, CSS, and JavaScript following modern web development best practices.

## 🚀 Features

- **Responsive Design**: Mobile-first approach with seamless experience across all devices
- **Modern UI/UX**: Clean, professional design with smooth animations and interactions
- **Performance Optimized**: Fast loading times with optimized assets and code
- **SEO Friendly**: Proper meta tags, semantic HTML, and structured content
- **Accessibility**: WCAG compliant with proper ARIA labels and keyboard navigation
- **Cross-browser Compatible**: Works perfectly on all modern browsers

## 📁 Project Structure

```
Pracinov_site1/
├── assets/
│   ├── css/
│   │   └── styles.css          # Main stylesheet with CSS variables and responsive design
│   ├── js/
│   │   └── main.js             # JavaScript functionality and interactions
│   └── images/                 # Image assets (to be added)
├── index.html                  # Homepage
├── about.html                  # About Us page
├── services.html               # Services page
├── portfolio.html              # Portfolio page
├── life.html                   # Life@ Pracinov page
├── careers.html                # Careers page
├── package.json                # Project dependencies and scripts
├── vite.config.js              # Vite configuration for development
└── README.md                   # Project documentation
```

## 🛠️ Technologies Used

- **HTML5**: Semantic markup with modern standards
- **CSS3**: Custom properties, Flexbox, Grid, animations
- **JavaScript (ES6+)**: Modern JavaScript with modules and best practices
- **Tailwind CSS**: Utility-first CSS framework (via CDN)
- **Material Icons**: Google's Material Design icons
- **Vite**: Modern build tool for development and production
- **Google Fonts**: Inter font family for typography

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager

### Installation

1. **Clone or download the project**
   ```bash
   cd Pracinov_site1
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```
   The site will be available at `http://localhost:3000`

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run serve` - Serve production build with http-server

## 📱 Pages Overview

### Homepage (`index.html`)
- Hero section with compelling value proposition
- Feature highlights showcasing company strengths
- Client testimonials with ratings
- Company values and story section

### About Us (`about.html`)
- Company story and mission
- Core values and principles
- Leadership team profiles
- Company statistics and achievements

### Services (`services.html`)
- Comprehensive service offerings
- Technology stack showcase
- Development process overview
- Contact form for consultations

### Portfolio (`portfolio.html`)
- Featured project case studies
- Industry expertise showcase
- Technology demonstrations
- Client success metrics

### Life@ Pracinov (`life.html`)
- Company culture overview
- Employee benefits and perks
- Team member spotlights
- Office environment showcase

### Careers (`careers.html`)
- Open job positions
- Application process overview
- Company benefits
- Application form

## 🎨 Design System

### Color Palette
- **Primary**: #4F46E5 (Indigo)
- **Secondary**: #7C3AED (Purple)
- **Accent**: #6366F1 (Blue)
- **Text Dark**: #1F2937
- **Text Light**: #6B7280
- **Background**: #F3F4F6

### Typography
- **Font Family**: Inter (Google Fonts)
- **Weights**: 400, 500, 600, 700, 800

### Components
- Responsive navigation with mobile menu
- Reusable card components
- Form elements with validation
- Button variants and states
- Notification system

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## ⚡ Performance Features

- **CSS Variables**: Consistent theming and easy customization
- **Optimized Images**: Proper sizing and lazy loading ready
- **Minimal JavaScript**: Lightweight, vanilla JS implementation
- **CSS Grid & Flexbox**: Modern layout techniques
- **Smooth Animations**: Hardware-accelerated transitions

## 🔧 Customization

### Colors
Update CSS variables in `assets/css/styles.css`:
```css
:root {
  --primary-color: #4F46E5;
  --secondary-color: #7C3AED;
  /* ... other variables */
}
```

### Content
- Update text content directly in HTML files
- Replace placeholder images with actual company photos
- Modify contact information in footer sections

### Styling
- Add custom styles to `assets/css/styles.css`
- Use existing utility classes for consistency
- Follow the established design system

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Static Hosting
The built files in the `dist/` folder can be deployed to:
- Netlify
- Vercel
- GitHub Pages
- AWS S3
- Any static hosting service

## 📞 Support

For questions or support regarding this website:
- Email: <EMAIL>
- Phone: +1 (234) 567-890

## 📄 License

This project is proprietary to Pracinov. All rights reserved.

---

Built with ❤️ by the Pracinov team
