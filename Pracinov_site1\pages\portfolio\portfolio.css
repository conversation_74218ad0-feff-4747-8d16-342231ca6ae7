/* Portfolio Page Specific Styles */

/* Portfolio Grid */
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.portfolio-item {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
}

.portfolio-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.portfolio-image {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #6366F1, #8B5CF6);
    position: relative;
    overflow: hidden;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.05);
}

.portfolio-content {
    padding: 1.5rem;
}

.portfolio-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.5rem;
}

.portfolio-description {
    color: #6B7280;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.portfolio-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.portfolio-tag {
    background: #F3F4F6;
    color: #6B7280;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.portfolio-link {
    display: inline-flex;
    align-items: center;
    color: #6366F1;
    font-weight: 600;
    text-decoration: none;
    transition: color 0.3s ease;
}

.portfolio-link:hover {
    color: #4F46E5;
}

/* Filter Buttons */
.filter-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 3rem;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid #E5E7EB;
    background: white;
    color: #6B7280;
    border-radius: 2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    border-color: #6366F1;
    background: #6366F1;
    color: white;
}

/* Modal Styles */
.portfolio-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.portfolio-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 1rem;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    margin: 2rem;
    position: relative;
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

/* Responsive Design */
@media (max-width: 768px) {
    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .filter-buttons {
        gap: 0.5rem;
    }
    
    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .modal-content {
        margin: 1rem;
    }
}

@media (max-width: 640px) {
    .portfolio-image {
        height: 180px;
    }
    
    .portfolio-content {
        padding: 1rem;
    }
    
    .portfolio-title {
        font-size: 1.125rem;
    }
}
