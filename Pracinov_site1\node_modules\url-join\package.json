{"name": "url-join", "version": "4.0.1", "description": "Join urls and normalize as in path.join.", "main": "lib/url-join.js", "scripts": {"test": "mocha --require should"}, "repository": {"type": "git", "url": "git://github.com/jfromaniello/url-join.git"}, "keywords": ["url", "join"], "author": "<PERSON> <<EMAIL>> (http://joseoncode.com)", "license": "MIT", "devDependencies": {"conventional-changelog": "^1.1.10", "mocha": "^3.2.0", "should": "~1.2.1"}}