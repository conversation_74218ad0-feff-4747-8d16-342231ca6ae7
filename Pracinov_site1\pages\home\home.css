/* Home Page Specific Styles */

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
    position: relative;
    overflow: hidden;
}

.hero-image-container {
    position: relative;
    z-index: 0;
}

.hero-image-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.5) 0%, rgba(124, 58, 237, 0.5) 100%);
    mix-blend-mode: overlay;
    z-index: 1;
    border-radius: 0.5rem;
}

.hero-image {
    mix-blend-mode: screen;
    opacity: 0.8;
    filter: grayscale(50%) brightness(1.2) contrast(1.1);
    position: relative;
    z-index: 0;
}

.hero-content-container {
    position: relative;
    z-index: 2;
}

/* Client Success Background */
.client-success-bg {
    background-color: #4338CA;
    background-image: radial-gradient(circle at top left, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 30%),
                      radial-gradient(circle at bottom right, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 40%);
}

/* Testimonial Cards */
.testimonial-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.testimonial-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.testimonial-card::before {
    content: """;
    position: absolute;
    top: -10px;
    left: 10px;
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.1);
    z-index: 0;
    line-height: 1;
}

/* Background Gradients */
.bg-gradient-light-blue {
    background: linear-gradient(180deg, #E0F2FE 0%, #BFDBFE 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 80vh;
    }
    
    .hero-content-container h1 {
        font-size: 2.5rem;
        line-height: 1.2;
    }
    
    .hero-content-container p {
        font-size: 1.125rem;
    }
    
    .testimonial-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 640px) {
    .hero-content-container h1 {
        font-size: 2rem;
    }
    
    .hero-content-container p {
        font-size: 1rem;
    }
    
    .hero-section {
        min-height: 70vh;
    }
}
