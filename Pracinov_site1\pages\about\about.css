/* About Page Specific Styles */

/* About Hero Section */
.about-hero {
    background: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);
    position: relative;
}

.about-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%236366F1" fill-opacity="0.05"%3E%3Ccircle cx="7" cy="7" r="1"/%3E%3Ccircle cx="27" cy="7" r="1"/%3E%3Ccircle cx="47" cy="7" r="1"/%3E%3Ccircle cx="7" cy="27" r="1"/%3E%3Ccircle cx="27" cy="27" r="1"/%3E%3Ccircle cx="47" cy="27" r="1"/%3E%3Ccircle cx="7" cy="47" r="1"/%3E%3Ccircle cx="27" cy="47" r="1"/%3E%3Ccircle cx="47" cy="47" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
    opacity: 0.5;
}

/* Values Grid Animation */
.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

/* Timeline Styles (for future use) */
.timeline {
    position: relative;
    padding: 2rem 0;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #6366F1, #8B5CF6);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
    padding: 0 2rem;
}

.timeline-item:nth-child(odd) {
    text-align: right;
    padding-right: calc(50% + 2rem);
}

.timeline-item:nth-child(even) {
    text-align: left;
    padding-left: calc(50% + 2rem);
}

.timeline-marker {
    position: absolute;
    top: 0;
    left: 50%;
    width: 16px;
    height: 16px;
    background: #6366F1;
    border: 4px solid white;
    border-radius: 50%;
    transform: translateX(-50%);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
}

/* Team Member Cards */
.team-member {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.team-member:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.team-member img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.team-member-info {
    padding: 1.5rem;
    text-align: center;
}

.team-member h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.5rem;
}

.team-member p {
    color: #6B7280;
    margin-bottom: 1rem;
}

.team-social {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
}

.team-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: #F3F4F6;
    border-radius: 50%;
    color: #6B7280;
    transition: all 0.3s ease;
}

.team-social a:hover {
    background: #6366F1;
    color: white;
    transform: translateY(-2px);
}

/* Stats Section */
.stats-section {
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
    color: white;
}

.stat-item {
    text-align: center;
    padding: 2rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #FFFFFF, #E0E7FF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 1.125rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

/* Responsive Design */
@media (max-width: 768px) {
    .timeline::before {
        left: 2rem;
    }
    
    .timeline-item {
        padding-left: 4rem;
        padding-right: 1rem;
        text-align: left;
    }
    
    .timeline-item:nth-child(odd) {
        padding-left: 4rem;
        padding-right: 1rem;
        text-align: left;
    }
    
    .timeline-marker {
        left: 2rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
    
    .values-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 640px) {
    .about-hero h1 {
        font-size: 2.5rem;
    }
    
    .about-hero p {
        font-size: 1.125rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .team-member img {
        height: 200px;
    }
}
